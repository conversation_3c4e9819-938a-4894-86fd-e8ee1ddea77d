/**
 * ExSprite
 * 组件类 - 从编译后的JS反编译生成
 */

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
    },

    start: function () {
        // TODO: 实现方法逻辑
    },

    onLoad: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
